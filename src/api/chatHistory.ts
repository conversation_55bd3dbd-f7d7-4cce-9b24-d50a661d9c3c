/*
 * 历史对话相关API接口
 */

import request from '@/utils/request'

// 历史对话项接口定义
export interface HistoryConversationItem {
  sessionId: string
  sessionName: string
  lastMessageTime: number
}

// 获取历史对话列表的请求参数
export interface GetHistoryConversationsParams {
  page?: number
  pageSize?: number
  keyword?: string // 搜索关键词
  type?: 'C2C' | 'GROUP' | 'ALL' // 对话类型筛选
}

// 获取历史对话列表的响应数据
export interface GetHistoryConversationsResponse {
  code: number
  message: string
  data: {
    list: HistoryConversationItem[]
    total: number
    page: number
    pageSize: number
    hasMore: boolean
  }
}

// 删除历史对话的请求参数
export interface DeleteConversationParams {
  conversationID: string
}

// 清空历史对话的请求参数
export interface ClearConversationParams {
  conversationID: string
}

// IM会话项接口定义 (新增)
export interface IMSessionItem {
  id: number
  sessionId: string
  sessionName: string
  lastMessageTime: number
}

// 获取IM会话列表的响应数据 (新增)
export interface GetIMSessionListResponse {
  code: number
  msg: string
  data: IMSessionItem[]
}

// 修改会话名称的请求参数 (新增)
export interface ModifySessionNameParams {
  sessionId: string
  sessionName: string
}

// 删除会话的请求参数 (新增)
export interface DeleteSessionParams {
  sessionId: string
}

// 创建群组会话的请求参数 (新增)
export interface CreateGroupSessionParams {
  groupId: string
  sessionName?: string
  userId?: string
}

// 创建群组会话的响应数据 (新增)
export interface CreateGroupSessionResponse {
  code: number
  msg: string
  data: {
    sessionId: string
    groupId: string
    sessionName: string
    createTime: number
  }
}

/**
 * 获取用户历史对话列表
 * @param params 请求参数
 * @returns Promise<GetHistoryConversationsResponse>
 */
export function getHistoryConversations(params: GetHistoryConversationsParams = {}) {
  return request<GetHistoryConversationsResponse>({
    url: '/api/chat/history/conversations',
    method: 'get',
    params: {
      page: 1,
      pageSize: 20,
      ...params,
    },
  })
}

/**
 * 删除指定的历史对话
 * @param params 请求参数
 * @returns Promise<any>
 */
export function deleteConversation(params: DeleteConversationParams) {
  return request({
    url: '/api/chat/history/conversation',
    method: 'delete',
    data: params,
  })
}

/**
 * 清空指定对话的历史消息
 * @param params 请求参数
 * @returns Promise<any>
 */
export function clearConversationHistory(params: ClearConversationParams) {
  return request({
    url: '/api/chat/history/clear',
    method: 'post',
    data: params,
  })
}

/**
 * 获取对话详情
 * @param conversationID 对话ID
 * @returns Promise<any>
 */
export function getConversationDetail(conversationID: string) {
  return request({
    url: `/api/chat/history/conversation/${conversationID}`,
    method: 'get',
  })
}

/**
 * 搜索历史对话
 * @param keyword 搜索关键词
 * @param params 其他参数
 * @returns Promise<GetHistoryConversationsResponse>
 */
export function searchHistoryConversations(keyword: string, params: Omit<GetHistoryConversationsParams, 'keyword'> = {}) {
  return getHistoryConversations({
    keyword,
    ...params,
  })
}

/**
 * 标记对话为已读
 * @param conversationID 对话ID
 * @returns Promise<any>
 */
export function markConversationAsRead(conversationID: string) {
  return request({
    url: '/api/chat/history/mark-read',
    method: 'post',
    data: { conversationID },
  })
}

/**
 * 置顶/取消置顶对话
 * @param conversationID 对话ID
 * @param isPinned 是否置顶
 * @returns Promise<any>
 */
export function pinConversation(conversationID: string, isPinned: boolean) {
  return request({
    url: '/api/chat/history/pin',
    method: 'post',
    data: {
      conversationID,
      isPinned,
    },
  })
}

/**
 * 获取对话统计信息
 * @returns Promise<any>
 */
export function getConversationStats() {
  return request({
    url: '/api/chat/history/stats',
    method: 'get',
  })
}

/**
 * 获取IM会话列表
 * @returns Promise<GetIMSessionListResponse>
 */
export function getIMSessionList(params: any) {
  return request<GetIMSessionListResponse>({
    url: '/clientApi/v1/txy/im/anon/get/session/list',
    method: 'get',
    params,
  })
}

/**
 * 修改会话名称
 * @param params 请求参数
 * @returns Promise<any>
 */
export function modifySessionName(params: ModifySessionNameParams) {
  return request({
    url: '/clientApi/v1/txy/im/anon/modify/session/name',
    method: 'post',
    data: params,
  })
}

/**
 * 删除会话
 * @param params 请求参数
 * @returns Promise<any>
 */
export function deleteSession(params: DeleteSessionParams) {
  return request({
    url: '/clientApi/v1/txy/im/anon/delete/session',
    method: 'post',
    data: params,
  })
}

/**
 * 获取会话记录
 * @param params 请求参数
 * @returns Promise<any>
 */
export function getSessionMessage(params: any) {
  return request({
    url: '/clientApi/v1/txy/im/anon/get/session/message',
    method: 'get',
    params,
  })
}

/**
 * 创建群组会话
 * @param params 请求参数
 * @returns Promise<CreateGroupSessionResponse>
 */
export function createGroupSession(params: CreateGroupSessionParams) {
  return request<CreateGroupSessionResponse>({
    url: '/clientApi/v1/txy/im/anon/create/group/session',
    method: 'post',
    data: params,
  })
}
