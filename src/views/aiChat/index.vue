<!--
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-17 10:28:29
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-07-28 14:57:29
 * @FilePath: /miaobi-admin-magic-touch/src/views/aiChat/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="ai-chat-container">
    <div v-if="loading || isClearingData" class="loading-container">
      <i class="el-icon-loading"></i>
      <div class="welcome-container">
        <div class="ai-icon">
          <el-icon>
            <MagicStick />
          </el-icon>
        </div>
        <h1 class="welcome-title">欢迎使用神笔马良</h1>
        <p class="welcome-subtitle">智能对话，创意无限</p>
      </div>
    </div>
    <component :is="ChatComponent" v-else :to-account="toAccount" type="c2group" :userId="imUserId" />

    <!-- 引入全屏视频组件 -->
    <FullscreenVideo />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, inject, defineAsyncComponent } from 'vue'
const ChatComponent = defineAsyncComponent(() => import('@/components/Chat/index.vue'))
import FullscreenVideo from '@/components/FullscreenVideo/index.vue'
import { LocationQueryValue, useRoute } from 'vue-router'
import { getSign } from '@/api/common'
import AuthModal from '@/plugins/auth'
const { hasPermi } = AuthModal
import TUIChatEngine, { TUITranslateService, TUIChatService, TUIStore, StoreName, TUIConversationService, TUIGroupService } from '@tencentcloud/chat-uikit-engine'
import { getAiChatSign, clearAllMessage, getToolsFour, getToolsAll } from '@/api/aiChat'
import { createGroupSession } from '@/api/chatHistory'
import { sendCustomMessage } from '@/utils/customIM'
import useChatStore from '@/store/modules/chat'
import clientReceiver from '@/utils/clientReceiver'
const chatStore = useChatStore()
const route = useRoute()
const toAccount = ref('')
const loading = ref(true)
const isClearingData = ref(false)
const imUserId = ref('')
const VITE_APP_ENV = ref(import.meta.env.VITE_APP_ENV)
const { query } = route
const { room, roomid, id, userid, courseid, role, usertype, appName, _t } = query

const T_STORAGE_KEY = 'chat_t_param'
TUIChatEngine.setLogLevel(4)

const roleType: { [key: string]: string } = {
  student: 'user',
  teacher: 'teacher',
}
const SpecialCourseIds: string[] = ['85', '88']
// 判断key可能是数组或者字符串或者null，取数组的第一项或者字符串或者null
const getQueryValue = (key: LocationQueryValue | LocationQueryValue[]) => {
  if (Array.isArray(key)) {
    return key[0]
  }
  return key
}

// 检查_t参数并与本地存储比较
const checkTParam = () => {
  const tParam = getQueryValue(_t)
  if (tParam) {
    const storedTParam = localStorage.getItem(T_STORAGE_KEY)
    if (storedTParam && tParam !== storedTParam) {
      console.log('_t参数已变更，执行刷新操作')
      // 这里执行需要的方法
      handleTParamChanged(tParam)
    } else {
      console.log('_t参数未变更')
    }
    // 更新存储的_t值
    localStorage.setItem(T_STORAGE_KEY, tParam as string)
  }
}

// 当_t参数变更时执行的方法
const handleTParamChanged = (newTValue: string) => {
  // 在这里实现_t参数变更后需要执行的逻辑
  console.log('处理_t参数变更:', newTValue)

  // 设置清空数据状态为true
  isClearingData.value = true

  // 等待TUIChatEngine就绪后再执行清空操作
  const waitForTUIReady = () => {
    if (TUIChatEngine.isReady() && toAccount.value) {
      // 检查会话是否存在并可访问
      const conversationID = `GROUP${toAccount.value}`

      // 尝试切换到该会话来验证其存在性
      TUIConversationService.switchConversation(conversationID)
        .then(() => {
          console.log('会话已存在并可访问，等待1秒确保会话完全加载')
          // 增加延迟确保会话完全加载
          setTimeout(() => {
            // 再次检查会话是否已完全准备好
            const conversationList = TUIStore.getData(StoreName.CONV, 'conversationList') || []
            const conversation = conversationList.find((item: any) => item.conversationID === conversationID)

            if (conversation) {
              console.log('会话已完全加载，开始清空聊天记录')
              clearChatHistory()
            } else {
              console.log('会话尚未在列表中，需要额外等待')
              setTimeout(waitForTUIReady, 1500)
            }
          }, 1000)
        })
        .catch((error: unknown) => {
          console.log('会话尚未创建或无法访问，等待创建会话再清空', error)
          // 等待更长时间再尝试
          setTimeout(waitForTUIReady, 1500)
        })
    } else {
      console.log('等待TUIChatEngine初始化完成...')
      setTimeout(waitForTUIReady, 1500) // 增加等待时间
    }
  }

  // 清空聊天记录的具体实现
  const clearChatHistory = () => {
    // 确定会话ID
    const conversationID = `GROUP${toAccount.value}`

    try {
      // 获取当前会话对象
      const conversationList = TUIStore.getData(StoreName.CONV, 'conversationList') || []
      const conversation = conversationList.find((item: any) => item.conversationID === conversationID)

      if (!conversation) {
        console.error('无法清空聊天记录：未找到有效会话对象')
        isClearingData.value = false // 结束清理状态
        return
      }

      // 清除历史消息
      TUIChatService.clearHistoryMessage(conversationID)
        .then(() => {
          // 调用API清空群组消息
          if (toAccount.value) {
            clearAllMessage({ groupId: toAccount.value })
              .then(() => {
                console.log('清空聊天记录成功')

                // 发送自定义控制消息
                sendCustomMessage({
                  data: {
                    businessID: 'ai_event',
                    content: {
                      name: 'control',
                      data: {},
                    },
                  },
                })
                  .then(() => {
                    console.log('发送控制消息成功')
                    // 完成所有操作后，设置清空状态为false
                    isClearingData.value = false
                  })
                  .catch((error: unknown) => {
                    console.error('发送控制消息失败:', error)
                    isClearingData.value = false // 出错时也结束清理状态
                  })

                // 更新状态
                TUIStore.update(StoreName.CHAT, 'isCompleted', true)
              })
              .catch((error: unknown) => {
                console.error('API清空聊天记录失败:', error)
                isClearingData.value = false // 出错时也结束清理状态
              })
          } else {
            isClearingData.value = false // 如果没有toAccount，也结束清理状态
          }
        })
        .catch((error: unknown) => {
          console.error('清空聊天记录失败:', error)

          // 尝试备选方案：直接更新消息列表
          try {
            TUIStore.update(StoreName.CHAT, 'messageList', [])
            console.log('使用备选方案清空聊天记录')

            // 继续调用API清空群组消息
            if (toAccount.value) {
              clearAllMessage({ groupId: toAccount.value })
                .then(() => {
                  console.log('API清空聊天记录成功')
                  isClearingData.value = false // 备选方案成功，结束清理状态
                })
                .catch((err: unknown) => {
                  console.error('API清空聊天记录失败:', err)
                  isClearingData.value = false // 出错时也结束清理状态
                })
            } else {
              isClearingData.value = false // 如果没有toAccount，也结束清理状态
            }
          } catch (err) {
            console.error('备选清空方案也失败:', err)
            isClearingData.value = false // 所有方案都失败，结束清理状态
          }
        })
    } catch (error) {
      console.error('获取会话对象失败:', error)
      isClearingData.value = false // 出错时也结束清理状态
    }
  }

  // 开始等待TUIChatEngine就绪
  waitForTUIReady()
}

function setupWebViewJavascriptBridge(callback: Function) {
  if (window.WebViewJavascriptBridge) {
    //@ts-ignore
    return callback(WebViewJavascriptBridge)
  }
  if (window.WVJBCallbacks) {
    return window.WVJBCallbacks.push(callback)
  }
  window.WVJBCallbacks = [callback]
  var WVJBIframe = document.createElement('iframe')
  WVJBIframe.style.display = 'none'
  WVJBIframe.src = 'https://__bridge_loaded__'
  document.documentElement.appendChild(WVJBIframe)
  setTimeout(function () {
    document.documentElement.removeChild(WVJBIframe)
  }, 0)
}

onMounted(async () => {
  const courseId: string = (getQueryValue(courseid) as string) || ''
  console.log("🚀 ~ onMounted ~ courseId:", courseId, typeof courseId)

  // 监听客户端数据的处理函数
  const handleClientData = (data: any) => {
    console.log('AI聊天页面收到客户端数据:', data)

    // 判断data的数据类型，如果是字符串且为JSON格式则转换为对象
    let processedData = data;
    if (typeof data === 'string') {
      try {
        processedData = JSON.parse(data);
        console.log('已将JSON字符串转换为对象:', processedData);
      } catch (error) {
        console.error('数据不是有效的JSON字符串:', error);
      }
    }

    // 在这里处理客户端传来的数据
    // 例如：根据数据类型执行不同操作
    // chatStore?.setSpecialCourseConfig(processedData);
    const hasSpecialCourseConfig = Object.keys(processedData)?.length
    if (hasSpecialCourseConfig) {
      chatStore?.setTool('')
      chatStore?.setTools([])
      chatStore?.setAssistantShow(false)
      chatStore?.setTool(processedData?.functionName)
      chatStore?.setTools([{
        functionClassify: processedData?.functionName,
        toolList: processedData
      }])
      chatStore?.setAssistantShow(true)

    }

  }

  if (SpecialCourseIds?.includes(courseId)) {
    // 特殊课程ID的处理逻辑
    chatStore?.setSpecialCourse(true)
  } else {
    chatStore?.setSpecialCourse(false)
  }

  clientReceiver.onReceive(handleClientData)

  // 关闭音频
  setupWebViewJavascriptBridge(function (bridge) {
    bridge.registerHandler('webViewWillDisappear', function (data, responseCallback) {
      chatStore.setMute(new Date().getTime())
    })
  })
  // 保存重要信息
  const PROGRESS_STORAGE_KEY = 'chat_progress_data'
  const COMPLETED_VIDEOS_KEY = 'chat_completed_videos'
  const progressData = localStorage.getItem(PROGRESS_STORAGE_KEY)
  const savedTParam = localStorage.getItem(T_STORAGE_KEY)
  const completedVideos = localStorage.getItem(COMPLETED_VIDEOS_KEY)
  // 选择性清除localStorage，而不是全部清除
  const keysToKeep = [PROGRESS_STORAGE_KEY, T_STORAGE_KEY, COMPLETED_VIDEOS_KEY]
  Object.keys(localStorage).forEach(key => {
    if (!keysToKeep.includes(key)) {
      localStorage.removeItem(key)
    }
  })

  // 确保重要数据被保留
  if (progressData) {
    localStorage.setItem(PROGRESS_STORAGE_KEY, progressData)
  }

  if (savedTParam) {
    localStorage.setItem(T_STORAGE_KEY, savedTParam)
  }

  if (completedVideos) {
    localStorage.setItem(COMPLETED_VIDEOS_KEY, completedVideos)
  }

  // 检查_t参数 - 只有老师端才执行
  // if (getQueryValue(role) === 'teacher') {
  checkTParam()
  // }

  const init = async () => {
    loading.value = true
    try {
      const payload = {
        roomId: getQueryValue(room) || getQueryValue(roomid),
        userId: getQueryValue(id) || getQueryValue(userid),
        courseId: getQueryValue(courseid),
        userType: roleType[getQueryValue(role) || getQueryValue(usertype) || ''],
        appName: getQueryValue(appName) || '1v1',
      }

      const res = await getAiChatSign(payload)
      console.log('🚀 ~ init ~ res:', res)
      if (!res?.data?.groupId) {
        console.error('获取群组ID失败')

        return
      }
      chatStore.setGroupId(res?.data?.groupId)
      toAccount.value = res?.data?.groupId
      imUserId.value = res?.data?.imUserId
      // 登录IM系统
      const SDKAppID = import.meta.env.VITE_APP_TENCENT_IM_SDK_APP_ID
      const SDKAppID_MIAOBI = import.meta.env.VITE_APP_TENCENT_IM_SDK_APP_ID_MIAOBI
      console.log('🚀 ~ init ~ SDKAppID:', SDKAppID)
      console.log('🚀 ~ init ~ SDKAppID_MIAOBI:', SDKAppID_MIAOBI)
      try {
        const loginRes = await TUIChatEngine.login({
          SDKAppID: SDKAppID_MIAOBI,
          userID: res.data?.imUserId,
          userSig: res.data?.sign,
          useUploadPlugin: true,
        })

        console.log('IM登录成功:', loginRes)
        // 等待TUIChatEngine完全初始化
        let checkReady = setInterval(() => {
          if (TUIChatEngine.isReady()) {
            clearInterval(checkReady)

            // 检查用户是否为群组成员，如果不是则尝试加入群组
            checkAndJoinGroup(toAccount.value)
              .then(() => {
                loading.value = false
                console.log('TUIChatEngine已就绪，群组ID:', toAccount.value)
                // 创建群组会话
                return createGroupSession({
                  groupId: toAccount.value,
                })
              })
              .then((sessionResult) => {
                console.log('群组会话创建成功:', sessionResult)
              })
              .catch(error => {
                console.error('检查或加入群组失败，或创建会话失败:', error)
                loading.value = false
              })
          }
        }, 200)
      } catch (error) {
        console.error('TUIChatEngine.login 失败:', error)
        loading.value = false
      }
    } catch (error) {
      console.error('获取聊天签名失败:', error)
      loading.value = false
    }
  }

  // 检查用户是否为群组成员，如果不是则尝试加入群组
  const checkAndJoinGroup = async (groupId: string) => {
    console.log('🚀 ~ checkAndJoinGroup ~ groupId:', groupId)
    if (!groupId) {
      console.error('群组ID为空，无法检查或加入群组')

      return Promise.reject('群组ID为空')
    }

    try {
      // 添加标准化的群组ID前缀
      const standardGroupId = groupId
      console.log('🚀 ~ checkAndJoinGroup ~ standardGroupId:', standardGroupId)
      // 尝试获取群组资料，如果成功则表明用户是群组成员
      await TUIGroupService.getGroupProfile({ groupID: standardGroupId })
      console.log('用户已是群组成员，无需加入')
      return Promise.resolve()
    } catch (error: any) {
      // 如果错误码为10007，表示用户不是群组成员
      if (error.code === 10007) {
        console.log('用户不是群组成员，尝试加入群组')

        try {
          // 尝试加入群组
          const joinRes = await TUIGroupService.joinGroup({ groupID: groupId })
          console.log('成功加入群组:', joinRes)
          return Promise.resolve()
        } catch (joinError: any) {
          // 如果加入失败，检查是否是因为已申请等待审批
          if (joinError.code === 10013) {
            console.log('已申请加入群组，等待审批')
            return Promise.resolve()
          }

          // 其他加入失败情况
          console.error('加入群组失败:', joinError)
          return Promise.reject(joinError)
        }
      } else {
        // 其他错误
        console.error('获取群组资料失败，错误码非10007:', error)
        return Promise.reject(error)
      }
    }
  }

  getToolsAll({
    courseId: getQueryValue(courseid),
  }).then((res: any) => {
    console.log('res', res)
    chatStore.setTools(res.data)
  })
  getToolsFour({
    courseId: getQueryValue(courseid),
  }).then((res: any) => {
    console.log('res', res)
    chatStore.setFourTools(res.data)
  })
  await init()
})
</script>

<style lang="scss" scoped>
.ai-chat-container {
  width: 100%;
  height: 100svh;
  height: 100vh;
  // height: 100%;
  background: #f2f5fc;
  position: relative;

  :deep(.chat-container) {
    height: 100%;
  }

  .loading-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f7f7f7;
    z-index: 10;

    i {
      font-size: 32px;
      color: #409eff;
    }

    .loading-text {
      font-size: 16px;
      color: #606266;
    }
  }

  .welcome-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;

    .ai-icon {
      background: linear-gradient(135deg, #42d392, #647eff);
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

      i {
        color: white;
        font-size: 26px;
      }
    }

    .welcome-title {
      font-size: 22px;
      font-weight: 600;
      background: linear-gradient(to right, #42d392, #647eff);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      margin: 10px 0;
      text-align: center;
    }

    .welcome-subtitle {
      color: #606266;
      font-size: 16px;
      margin-top: 10px;
    }
  }
}
</style>
